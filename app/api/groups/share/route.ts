// app/api/groups/share/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { connectToDatabase } from '@/lib/dbconnect';
import { StokvelGroup } from '@/models/StokvelGroup';
import { User } from '@/models/User';
import { getCorsHeaders } from '@/lib/cors';
import { PromotionService } from '@/lib/services/promotionService';

const promotionService = new PromotionService();

interface ShareGroupRequest {
  groupId: string;
  platform: 'whatsapp' | 'facebook' | 'twitter' | 'linkedin' | 'email' | 'copy';
  customMessage?: string;
}

interface ShareGroupResponse {
  success: boolean;
  shareUrl?: string;
  message?: string;
  error?: string;
}

export async function POST(request: NextRequest) {
  const corsHeaders = getCorsHeaders(request.headers.get('origin'));

  try {
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    console.log('Share API - Auth header:', authHeader ? 'Present' : 'Missing');
    console.log('Share API - Token extracted:', token ? 'Present' : 'Missing');

    if (!token || token.trim() === '' || token === 'null' || token === 'undefined') {
      console.log('Share API - No valid token provided');
      return NextResponse.json(
        { error: 'Authentication required. Please login again.' },
        { headers: corsHeaders, status: 401 }
      );
    }

    let payload;
    try {
      payload = await verifyAccessToken(token);
      console.log('Share API - Token verified successfully for user:', payload.userId);
    } catch (error) {
      console.error('Share API - Token verification failed:', error);
      return NextResponse.json(
        { error: 'Invalid or expired token. Please login again.' },
        { headers: corsHeaders, status: 401 }
      );
    }

    if (!payload || !payload.userId) {
      console.log('Share API - Invalid payload or missing userId');
      return NextResponse.json(
        { error: 'Invalid token payload. Please login again.' },
        { headers: corsHeaders, status: 401 }
      );
    }

    await connectToDatabase();

    const body: ShareGroupRequest = await request.json();
    const { groupId, platform, customMessage } = body;

    if (!groupId || !platform) {
      return NextResponse.json(
        { error: 'Group ID and platform are required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Verify group exists and user is a member
    const group = await StokvelGroup.findById(groupId).populate('locationHierarchy');
    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check if user is a member of the group
    const isMember = group.members.some(
      (memberId: any) => memberId.toString() === payload.userId
    );

    if (!isMember) {
      return NextResponse.json(
        { error: 'You must be a member of this group to share it' },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Get user details for referral tracking
    const user = await User.findById(payload.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Generate share URL with referral tracking
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const shareUrl = `${baseUrl}/groups/join/${groupId}?ref=${user.referralCode}&utm_source=${platform}&utm_medium=group_share&utm_campaign=group_invitation`;

    // Generate platform-specific share URLs
    let platformShareUrl: string;
    const defaultMessage = customMessage || 
      `🎉 Join my Stokvel group "${group.name}"! We're saving money together through bulk buying. Click here to join: ${shareUrl}`;

    switch (platform) {
      case 'whatsapp':
        platformShareUrl = `https://wa.me/?text=${encodeURIComponent(defaultMessage)}`;
        break;
      case 'facebook':
        platformShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(defaultMessage)}`;
        break;
      case 'twitter':
        const twitterMessage = defaultMessage.length > 240 ? 
          `Join my Stokvel group "${group.name}"! Save money through bulk buying: ${shareUrl}` : 
          defaultMessage;
        platformShareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(twitterMessage)}`;
        break;
      case 'linkedin':
        platformShareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'email':
        const subject = `Join my Stokvel group: ${group.name}`;
        platformShareUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(defaultMessage)}`;
        break;
      case 'copy':
        platformShareUrl = shareUrl;
        break;
      default:
        platformShareUrl = shareUrl;
    }

    // Award points for sharing
    try {
      await promotionService.earnLoyaltyPoints({
        userId: payload.userId,
        action: 'group_share',
        additionalData: { 
          platform, 
          groupId,
          groupName: group.name 
        }
      });
    } catch (pointsError) {
      console.warn('Could not award sharing points:', pointsError);
    }

    const response: ShareGroupResponse = {
      success: true,
      shareUrl: platformShareUrl,
      message: `Group share link generated for ${platform}`
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error in group share API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
