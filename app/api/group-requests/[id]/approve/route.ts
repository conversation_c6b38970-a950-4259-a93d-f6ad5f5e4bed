// app/api/group-requests/[id]/approve/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { GroupRequest } from "@/models/GroupRequest";
import { StokvelGroup } from "@/models/StokvelGroup";
import { User } from "@/models/User";
import { NotificationService } from "@/lib/services/notificationService";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// POST /api/group-requests/[id]/approve - Approve group request and create group
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    console.log('🚀 POST /api/group-requests/[id]/approve - Starting approval process');
    await connectToDatabase();

    // Verify authentication (support both web and mobile clients)
    const clientType = req.headers.get('x-client-type') || 'web';
    let accessToken: string | undefined;

    console.log('🔍 Authentication check - clientType:', clientType);

    if (clientType === 'web') {
      // For web clients, read from the accessToken cookie
      accessToken = req.cookies.get('accessToken')?.value;
      console.log('🍪 Cookie accessToken found:', !!accessToken);
    } else {
      // For mobile clients, read from the Authorization header
      const authHeader = req.headers.get('authorization') || '';
      if (authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.substring(7);
      }
      console.log('📱 Authorization header found:', !!accessToken);
    }

    if (!accessToken) {
      console.log('❌ No access token found');
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    console.log('🔐 Verifying access token...');
    const payload = await verifyAccessToken(accessToken);
    if (!payload) {
      console.log('❌ Token verification failed');
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    console.log('✅ Token verified for user:', payload.id);

    // Verify admin role
    const adminUser = await User.findById(payload.id);
    console.log('👤 Admin user found:', !!adminUser, 'Role:', adminUser?.role);
    if (!adminUser || adminUser.role !== 'admin') {
      console.log('❌ Admin access denied');
      return NextResponse.json(
        { error: 'Admin access required' },
        { headers: corsHeaders, status: 403 }
      );
    }

    console.log('📋 Extracting params...');
    const { id } = await params;
    console.log('🆔 Request ID:', id);

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid request ID' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const body = await req.json();
    const { reviewNotes } = body;

    // Start a transaction for atomic operations
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Find the group request
      console.log('🔍 Finding group request with ID:', id);
      const groupRequest = await GroupRequest.findById(id).session(session);
      if (!groupRequest) {
        console.log('❌ Group request not found');
        await session.abortTransaction();
        return NextResponse.json(
          { error: 'Group request not found' },
          { headers: corsHeaders, status: 404 }
        );
      }

      console.log('📋 Group request found:', {
        id: groupRequest._id,
        status: groupRequest.status,
        userId: groupRequest.userId,
        userEmail: groupRequest.userEmail,
        userName: groupRequest.userName,
        requestedGroupName: groupRequest.requestedGroupName
      });

      // Check if already processed
      if (groupRequest.status !== 'pending') {
        console.log('❌ Request already processed:', groupRequest.status);
        await session.abortTransaction();
        return NextResponse.json(
          { error: `Request already ${groupRequest.status}` },
          { headers: corsHeaders, status: 400 }
        );
      }

      // Handle anonymous requests - require user registration first
      if (!groupRequest.userId) {
        console.log('❌ Anonymous request - cannot approve without user registration');
        await session.abortTransaction();
        return NextResponse.json(
          { error: 'Cannot approve anonymous requests. User must register first before group approval.' },
          { headers: corsHeaders, status: 400 }
        );
      }

      // Verify the requesting user still exists
      const requestingUser = await User.findById(groupRequest.userId).session(session);
      if (!requestingUser) {
        await session.abortTransaction();
        return NextResponse.json(
          { error: 'Requesting user not found' },
          { headers: corsHeaders, status: 404 }
        );
      }

      // Check if a group with the same name already exists in the location
      const existingGroup = await StokvelGroup.findOne({
        name: groupRequest.requestedGroupName,
        locationId: groupRequest.locationId
      }).session(session);

      if (existingGroup) {
        await session.abortTransaction();
        return NextResponse.json(
          { error: 'A group with this name already exists in this location' },
          { headers: corsHeaders, status: 409 }
        );
      }

      // Create the new StokvelGroup
      const newGroup = new StokvelGroup({
        name: groupRequest.requestedGroupName,
        description: groupRequest.groupDescription || `Stokvel group for ${groupRequest.fullLocationPath}`,
        admin: groupRequest.userId,
        members: [groupRequest.userId], // Add the requester as the first member
        locationId: groupRequest.locationId,
        totalSales: 0,
        avgOrderValue: 0,
        activeOrders: 0,
        bulkOrderThreshold: 1000,
        pendingOrderAmount: 0,
        deliveryStatus: 'pending'
      });

      await newGroup.save({ session });

      // Update the user's stokvelGroups array
      await User.findByIdAndUpdate(
        groupRequest.userId,
        { $addToSet: { stokvelGroups: newGroup._id } },
        { session }
      );

      // Update the group request status
      groupRequest.status = 'approved';
      groupRequest.reviewDate = new Date();
      groupRequest.reviewedBy = adminUser._id;
      groupRequest.reviewNotes = reviewNotes;
      groupRequest.createdGroupId = newGroup._id;

      await groupRequest.save({ session });

      // Commit the transaction
      await session.commitTransaction();

      // Populate the response data
      await groupRequest.populate([
        { path: 'userId', select: 'name email phone' },
        { path: 'reviewedBy', select: 'name email' },
        { path: 'createdGroupId', select: 'name _id' }
      ]);

      await newGroup.populate([
        { path: 'admin', select: 'name email' },
        { path: 'members', select: 'name email' },
        { path: 'locationId', select: 'name' }
      ]);

      // Send notification to user about approval
      try {
        await NotificationService.notifyGroupRequestApproved(
          groupRequest._id.toString(),
          newGroup._id.toString()
        );
      } catch (notificationError) {
        console.error('Failed to send approval notification:', notificationError);
        // Don't fail the request if notification fails
      }

      return NextResponse.json({
        success: true,
        message: 'Group request approved and group created successfully',
        request: groupRequest,
        createdGroup: newGroup
      }, { headers: corsHeaders, status: 200 });

    } catch (transactionError) {
      await session.abortTransaction();
      throw transactionError;
    } finally {
      session.endSession();
    }

  } catch (error) {
    console.error("Error approving group request:", error);
    
    if (error instanceof mongoose.Error.ValidationError) {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Handle duplicate key errors
    if (error instanceof Error && 'code' in error && error.code === 11000) {
      return NextResponse.json(
        { error: 'A group with this name already exists in this location' },
        { headers: corsHeaders, status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
