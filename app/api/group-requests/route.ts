// app/api/group-requests/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { GroupRequest } from "@/models/GroupRequest";
import { User } from "@/models/User";

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// GET /api/group-requests - Get all group requests (admin only) or user's requests
export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  console.log('GET /api/group-requests - Route handler called');
  
  return NextResponse.json({
    success: true,
    message: 'GET route is working',
    requests: []
  }, { headers: corsHeaders, status: 200 });
}

// POST /api/group-requests - Create new group request
export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  console.log('POST /api/group-requests - Route handler called');
  
  try {
    const body = await req.json();
    console.log('Request body received:', JSON.stringify(body, null, 2));
    
    // Simple test response
    return NextResponse.json({
      success: true,
      message: 'Test response - route is working',
      receivedData: body
    }, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error in POST /api/group-requests:", error);
    return NextResponse.json(
      { error: "Internal Server Error", details: error instanceof Error ? error.message : 'Unknown error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
