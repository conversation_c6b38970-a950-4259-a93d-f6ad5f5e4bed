// app/api/group-requests/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { verifyAccessToken } from "@/lib/auth";
import { GroupRequest } from "@/models/GroupRequest";
import { User } from "@/models/User";

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// GET /api/group-requests - Get all group requests (admin only) or user's requests
export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  console.log('GET /api/group-requests - Route handler called');

  try {
    await connectToDatabase();
    console.log('Database connected successfully');

    // Check for authentication
    const token = req.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const user = await User.findById(payload.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    // Build query
    const query: any = {};

    // For non-admin users, only show their own requests
    if (user.role !== 'admin') {
      query.userId = user._id;
    }

    if (status) {
      query.status = status;
    }

    if (search) {
      query.$or = [
        { requestedGroupName: { $regex: search, $options: 'i' } },
        { userName: { $regex: search, $options: 'i' } },
        { userEmail: { $regex: search, $options: 'i' } },
        { fullLocationPath: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await GroupRequest.countDocuments(query);

    // Get requests with pagination
    const requests = await GroupRequest.find(query)
      .populate('userId', 'name email phone')
      .populate('reviewedBy', 'name email')
      .populate('createdGroupId', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      requests,
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    }, { headers: corsHeaders, status: 200 });

  } catch (error) {
    console.error("Error fetching group requests:", error);
    return NextResponse.json(
      { error: "Internal Server Error", details: error instanceof Error ? error.message : 'Unknown error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// POST /api/group-requests - Create new group request
export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  console.log('POST /api/group-requests - Route handler called');

  try {
    await connectToDatabase();
    console.log('Database connected successfully');

    // Check for authentication (optional for group requests)
    const token = req.headers.get('authorization')?.replace('Bearer ', '');
    let authenticatedUser = null;

    if (token) {
      try {
        const payload = await verifyAccessToken(token);
        if (payload) {
          authenticatedUser = await User.findById(payload.userId);
        }
      } catch (error) {
        // Token is invalid, but we'll allow anonymous requests
        console.log('Invalid token provided, proceeding as anonymous request');
      }
    }

    const body = await req.json();
    console.log('Request body received:', JSON.stringify(body, null, 2));

    const {
      userId,
      userEmail,
      userName,
      userPhone,
      provinceId,
      provinceName,
      cityId,
      cityName,
      townshipId,
      townshipName,
      locationId,
      locationName,
      fullLocationPath,
      requestedGroupName,
      groupDescription
    } = body;

    // Validate required fields (userId is optional for anonymous requests)
    if (!userEmail || !userName || !locationId || !requestedGroupName) {
      return NextResponse.json(
        { error: 'Missing required fields: userEmail, userName, locationId, requestedGroupName' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // For authenticated users, verify user exists and matches token
    let requestUserId = null;
    if (authenticatedUser && userId) {
      if (authenticatedUser._id.toString() !== userId) {
        return NextResponse.json(
          { error: 'User ID mismatch' },
          { headers: corsHeaders, status: 403 }
        );
      }
      requestUserId = authenticatedUser._id;
    } else if (userId) {
      // If userId is provided but no valid token, check if user exists
      const user = await User.findById(userId);
      if (user) {
        requestUserId = user._id;
      }
    }
    // For anonymous users, requestUserId remains null

    // Check for existing pending request for same group name and location
    const existingRequest = await GroupRequest.findOne({
      requestedGroupName: requestedGroupName.trim(),
      locationId,
      status: { $ne: 'rejected' }
    });

    if (existingRequest) {
      return NextResponse.json(
        { error: 'A request for this group name already exists in this location' },
        { headers: corsHeaders, status: 409 }
      );
    }

    // Create the group request
    const groupRequestData: any = {
      userEmail: userEmail.toLowerCase().trim(),
      userName: userName.trim(),
      userPhone: userPhone?.trim(),
      provinceId,
      provinceName: provinceName.trim(),
      cityId,
      cityName: cityName.trim(),
      townshipId,
      townshipName: townshipName.trim(),
      locationId,
      locationName: locationName.trim(),
      fullLocationPath: fullLocationPath.trim(),
      requestedGroupName: requestedGroupName.trim(),
      groupDescription: groupDescription?.trim(),
      status: 'pending'
    };

    // Only add userId if we have a valid user
    if (requestUserId) {
      groupRequestData.userId = requestUserId;
    }

    const groupRequest = new GroupRequest(groupRequestData);
    await groupRequest.save();

    // Populate the response if userId exists
    if (requestUserId) {
      await groupRequest.populate('userId', 'name email phone');
    }

    console.log('Group request created successfully:', groupRequest._id);
    return NextResponse.json({
      success: true,
      message: 'Group request submitted successfully',
      request: groupRequest
    }, { headers: corsHeaders, status: 201 });

  } catch (error) {
    console.error("Error creating group request:", error);

    // Handle validation errors
    if (error instanceof mongoose.Error.ValidationError) {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Handle duplicate key errors
    if (error instanceof Error && 'code' in error && (error as any).code === 11000) {
      return NextResponse.json(
        { error: 'A request for this group name already exists in this location' },
        { headers: corsHeaders, status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Internal Server Error", details: error instanceof Error ? error.message : 'Unknown error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
