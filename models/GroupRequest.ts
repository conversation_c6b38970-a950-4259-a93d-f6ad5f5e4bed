// models/GroupRequest.ts

import mongoose, { Schema, Document, model } from 'mongoose';

export interface IGroupRequest extends Document {
  // User Information
  userId?: mongoose.Types.ObjectId; // Optional for anonymous requests
  userEmail: string;
  userName: string;
  userPhone?: string;
  userPassword?: string; // For anonymous users to register during approval
  
  // Location Hierarchy (for efficient querying and display)
  provinceId: mongoose.Types.ObjectId;
  provinceName: string;
  cityId: mongoose.Types.ObjectId;
  cityName: string;
  townshipId: mongoose.Types.ObjectId;
  townshipName: string;
  locationId: mongoose.Types.ObjectId;
  locationName: string;
  fullLocationPath: string; // "Province > City > Township > Location"
  
  // Group Details
  requestedGroupName: string;
  groupDescription?: string;
  
  // Request Management
  status: 'pending' | 'approved' | 'rejected';
  requestDate: Date;
  reviewDate?: Date;
  reviewedBy?: mongoose.Types.ObjectId; // Admin who reviewed
  reviewNotes?: string;
  
  // Auto-generated Group Info (populated when approved)
  createdGroupId?: mongoose.Types.ObjectId;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

const GroupRequestSchema: Schema<IGroupRequest> = new Schema(
  {
    // User Information
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false // Optional for anonymous requests
    },
    userEmail: { 
      type: String, 
      required: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    userName: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 100
    },
    userPhone: {
      type: String,
      trim: true,
      maxlength: 20
    },
    userPassword: {
      type: String,
      required: false, // Only required for anonymous requests
      minlength: 6,
      maxlength: 128 // Enough for hashed passwords
    },

    // Location Hierarchy
    provinceId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Province', 
      required: true 
    },
    provinceName: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 100
    },
    cityId: { 
      type: Schema.Types.ObjectId, 
      ref: 'City', 
      required: true 
    },
    cityName: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 100
    },
    townshipId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Township', 
      required: true 
    },
    townshipName: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 100
    },
    locationId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Location', 
      required: true 
    },
    locationName: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 100
    },
    fullLocationPath: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 500
    },
    
    // Group Details
    requestedGroupName: { 
      type: String, 
      required: true,
      trim: true,
      minlength: 3,
      maxlength: 50,
      match: [/^[a-zA-Z0-9\s\-_]+$/, 'Group name can only contain letters, numbers, spaces, hyphens, and underscores']
    },
    groupDescription: { 
      type: String,
      trim: true,
      maxlength: 500
    },
    
    // Request Management
    status: { 
      type: String, 
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending',
      required: true
    },
    requestDate: { 
      type: Date, 
      default: Date.now,
      required: true
    },
    reviewDate: { 
      type: Date 
    },
    reviewedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    reviewNotes: { 
      type: String,
      trim: true,
      maxlength: 1000
    },
    
    // Auto-generated Group Info
    createdGroupId: { 
      type: Schema.Types.ObjectId, 
      ref: 'StokvelGroup' 
    }
  },
  { 
    timestamps: true,
    // Add version key for optimistic concurrency control
    versionKey: '__v'
  }
);

// Indexes for efficient queries
GroupRequestSchema.index({ userId: 1, status: 1 });
GroupRequestSchema.index({ locationId: 1, status: 1 });
GroupRequestSchema.index({ status: 1, requestDate: -1 });
GroupRequestSchema.index({ reviewedBy: 1 });
GroupRequestSchema.index({ createdGroupId: 1 });

// Compound unique index to prevent duplicate group names per location
GroupRequestSchema.index(
  { requestedGroupName: 1, locationId: 1 }, 
  { 
    unique: true,
    partialFilterExpression: { status: { $ne: 'rejected' } } // Allow same name if previous request was rejected
  }
);

// Text index for search functionality
GroupRequestSchema.index({
  requestedGroupName: 'text',
  groupDescription: 'text',
  userName: 'text',
  userEmail: 'text',
  fullLocationPath: 'text'
});

// Virtual for populated user
GroupRequestSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated reviewer
GroupRequestSchema.virtual('reviewer', {
  ref: 'User',
  localField: 'reviewedBy',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated location hierarchy
GroupRequestSchema.virtual('location', {
  ref: 'Location',
  localField: 'locationId',
  foreignField: '_id',
  justOne: true,
  populate: {
    path: 'townshipId',
    populate: {
      path: 'cityId',
      populate: {
        path: 'provinceId'
      }
    }
  }
});

// Virtual for populated created group
GroupRequestSchema.virtual('createdGroup', {
  ref: 'StokvelGroup',
  localField: 'createdGroupId',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware to generate fullLocationPath
GroupRequestSchema.pre('save', function(next) {
  if (this.isModified('provinceName') || this.isModified('cityName') || 
      this.isModified('townshipName') || this.isModified('locationName')) {
    this.fullLocationPath = `${this.provinceName} > ${this.cityName} > ${this.townshipName} > ${this.locationName}`;
  }
  next();
});

// Pre-save middleware to set reviewDate when status changes to approved/rejected
GroupRequestSchema.pre('save', function(next) {
  if (this.isModified('status') && (this.status === 'approved' || this.status === 'rejected')) {
    if (!this.reviewDate) {
      this.reviewDate = new Date();
    }
  }
  next();
});

// Static methods for common queries
GroupRequestSchema.statics.findPendingRequests = function() {
  return this.find({ status: 'pending' }).sort({ requestDate: -1 });
};

GroupRequestSchema.statics.findByLocation = function(locationId: string) {
  return this.find({ locationId }).sort({ requestDate: -1 });
};

GroupRequestSchema.statics.findByUser = function(userId: string) {
  return this.find({ userId }).sort({ requestDate: -1 });
};

// Instance methods
GroupRequestSchema.methods.approve = function(reviewerId: string, notes?: string) {
  this.status = 'approved';
  this.reviewedBy = reviewerId;
  this.reviewDate = new Date();
  if (notes) this.reviewNotes = notes;
  return this.save();
};

GroupRequestSchema.methods.reject = function(reviewerId: string, notes?: string) {
  this.status = 'rejected';
  this.reviewedBy = reviewerId;
  this.reviewDate = new Date();
  if (notes) this.reviewNotes = notes;
  return this.save();
};

// Export the model
export const GroupRequest = mongoose.models.GroupRequest || model<IGroupRequest>('GroupRequest', GroupRequestSchema);
