"use client";

import { useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  Share2, 
  MessageCircle, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Mail, 
  Copy,
  Check
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { LoginPromptModal } from "@/components/modals/LoginPromptModal";

interface CompactGroupShareButtonProps {
  groupId: string;
  groupName: string;
  groupDescription?: string;
  memberCount?: number;
  className?: string;
}

interface ShareOption {
  name: string;
  icon: any;
  color: string;
  platform: 'whatsapp' | 'facebook' | 'twitter' | 'linkedin' | 'email' | 'copy';
}

export function CompactGroupShareButton({
  groupId,
  groupName,
  groupDescription,
  memberCount,
  className = ""
}: CompactGroupShareButtonProps) {
  const { user, isAuthenticated } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [copiedUrl, setCopiedUrl] = useState(false);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);

  const shareOptions: ShareOption[] = [
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'bg-green-500 hover:bg-green-600',
      platform: 'whatsapp'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'bg-blue-600 hover:bg-blue-700',
      platform: 'facebook'
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'bg-sky-500 hover:bg-sky-600',
      platform: 'twitter'
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      color: 'bg-blue-700 hover:bg-blue-800',
      platform: 'linkedin'
    },
    {
      name: 'Email',
      icon: Mail,
      color: 'bg-gray-600 hover:bg-gray-700',
      platform: 'email'
    },
    {
      name: 'Copy',
      icon: copiedUrl ? Check : Copy,
      color: 'bg-purple-600 hover:bg-purple-700',
      platform: 'copy'
    }
  ];

  const defaultMessage = `🎉 Join my Stokvel group "${groupName}"! We're saving money together through bulk buying. ${memberCount ? `Already ${memberCount} members strong!` : ''} Join us and start saving today!`;

  const handleShareButtonClick = () => {
    if (!isAuthenticated || !user) {
      // Show login prompt for non-authenticated users
      setShowLoginPrompt(true);
      return;
    }

    // Open share modal for authenticated users
    setIsOpen(true);
  };

  const handleShare = async (platform: string) => {
    setIsSharing(true);

    try {
      const token = localStorage.getItem('token');

      // Validate token before making request
      if (!token || token.trim() === '' || token === 'null' || token === 'undefined') {
        toast.error('Please login again to share groups');
        setIsSharing(false);
        return;
      }

      console.log('Sharing group with platform:', platform);

      const response = await fetch('/api/groups/share', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          groupId,
          platform,
          customMessage: defaultMessage
        })
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Share API error:', data);
        if (response.status === 401) {
          toast.error('Please login again to share groups');
        } else {
          toast.error(data.error || 'Failed to share');
        }
        return;
      }

      if (data.success && data.shareUrl) {
        if (platform === 'copy') {
          // Copy to clipboard
          await navigator.clipboard.writeText(data.shareUrl);
          setCopiedUrl(true);
          toast.success('Share link copied!');
          setTimeout(() => setCopiedUrl(false), 2000);
        } else {
          // Open share URL
          window.open(data.shareUrl, '_blank', 'width=600,height=400');
          toast.success(`Shared on ${platform}!`);
        }
        setIsOpen(false);
      } else {
        toast.error(data.error || 'Failed to share');
      }
    } catch (error) {
      console.error('Error sharing group:', error);
      toast.error('Failed to share group');
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <>
      <motion.button
        onClick={handleShareButtonClick}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        className={`p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors duration-200 touch-manipulation ${className}`}
        title={isAuthenticated ? "Share this group" : "Login to share and earn rewards"}
      >
        <Share2 className="w-4 h-4" />
      </motion.button>

      {/* Share Modal for Authenticated Users */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">Share "{groupName}"</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Group Info */}
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-900">{groupName}</h3>
            {memberCount && (
              <p className="text-sm text-gray-600 mt-1">
                {memberCount} members saving together
              </p>
            )}
          </div>

          {/* Share Options - Mobile Optimized Grid */}
          <div className="grid grid-cols-3 gap-3">
            {shareOptions.map((option, index) => (
              <motion.div
                key={option.platform}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <Button
                  onClick={() => handleShare(option.platform)}
                  disabled={isSharing}
                  className={`w-full h-16 p-2 ${option.color} text-white flex flex-col items-center justify-center space-y-1 touch-manipulation`}
                  variant="default"
                >
                  <option.icon className="h-5 w-5" />
                  <span className="text-xs font-medium">{option.name}</span>
                </Button>
              </motion.div>
            ))}
          </div>

          {/* Rewards Info */}
          <div className="text-center p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-xs text-green-700">
              🎁 Earn 50 points for sharing + 200 bonus points when someone joins!
            </p>
          </div>
        </div>
      </DialogContent>
      </Dialog>

      {/* Login Prompt Modal for Non-Authenticated Users */}
      <LoginPromptModal
        isOpen={showLoginPrompt}
        onClose={() => setShowLoginPrompt(false)}
        action="share this group"
        returnUrl={`/groups`}
        title="Login to Share & Earn Rewards"
        description={`Share "${groupName}" with your network and earn points! Login to track your referrals and get rewarded when people join through your links.`}
      />
    </>
  );
}
