// components/modals/ComprehensiveGroupRequestModal.tsx

"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  MapPin,
  Users,
  Plus,
  ChevronRight,
  ArrowLeft,
  Search,
  Building,
  Home,
  Sparkles,
  CheckCircle,
  AlertCircle,
  Loader2,
  User,
  Mail,
  Phone
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { createPortal } from "react-dom";
import { useAuth } from "@/context/AuthContext";
import { 
  useGetProvincesQuery,
  useLazyGetCitiesByProvinceQuery,
  useLazyGetTownshipsByCityQuery 
} from "@/lib/redux/features/locations/locationsApiSlice";
import { useCreateGroupRequestMutation, type CreateGroupRequestInput } from "@/lib/redux/features/groupRequests/groupRequestsApiSlice";
import type { Province, City, Township } from "@/types/locations";
import { cn } from "@/lib/utils";

interface ComprehensiveGroupRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface LocationSelection {
  province?: Province;
  city?: City;
  township?: Township;
}

interface UserInfo {
  name: string;
  email: string;
  phone?: string;
  password?: string; // For anonymous users to register during group request
}

type ModalStep = 'user-info' | 'province' | 'city' | 'township' | 'group-request' | 'success';

const modalVariants = {
  hidden: { 
    opacity: 0,
    scale: 0.95,
    y: 20
  },
  visible: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.4
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.95,
    y: 20,
    transition: {
      duration: 0.2
    }
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const stepTransition = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -20 },
  transition: { duration: 0.3 }
};

export function ComprehensiveGroupRequestModal({
  isOpen,
  onClose,
  onSuccess
}: ComprehensiveGroupRequestModalProps) {
  const { user } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [currentStep, setCurrentStep] = useState<ModalStep>('user-info');
  const [searchQuery, setSearchQuery] = useState("");
  const [isMobile, setIsMobile] = useState(false);
  
  // Form data states
  const [userInfo, setUserInfo] = useState<UserInfo>({
    name: "",
    email: "",
    phone: "",
    password: ""
  });
  const [locationSelection, setLocationSelection] = useState<LocationSelection>({});
  const [groupRequest, setGroupRequest] = useState({
    requestedGroupName: "",
    groupDescription: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // API hooks
  const { data: provincesData, isLoading: provincesLoading } = useGetProvincesQuery();
  const [getCities, { data: citiesData, isLoading: citiesLoading }] = useLazyGetCitiesByProvinceQuery();
  const [getTownships, { data: townshipsData, isLoading: townshipsLoading }] = useLazyGetTownshipsByCityQuery();
  const [createGroupRequest, { isLoading: isSubmitting }] = useCreateGroupRequestMutation();

  // Initialize component
  useEffect(() => {
    setMounted(true);
    
    // Check if mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Initialize user info if logged in
  useEffect(() => {
    if (user && isOpen) {
      setUserInfo({
        name: user.name || "",
        email: user.email || "",
        phone: user.phone || "",
        password: "" // No password needed for logged-in users
      });
      setCurrentStep('province'); // Skip user info step for logged-in users
    } else if (isOpen) {
      setCurrentStep('user-info');
    }
  }, [user, isOpen]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep('user-info');
      setSearchQuery("");
      setLocationSelection({});
      setGroupRequest({ requestedGroupName: "", groupDescription: "" });
      setErrors({});
      if (!user) {
        setUserInfo({ name: "", email: "", phone: "", password: "" });
      }
    }
  }, [isOpen, user]);

  if (!mounted) return null;

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const validateUserInfo = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!userInfo.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!userInfo.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userInfo.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (userInfo.phone && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(userInfo.phone)) {
      newErrors.phone = "Please enter a valid phone number";
    }

    // Password validation for anonymous users (not logged in)
    if (!user) {
      if (!userInfo.password || userInfo.password.length < 6) {
        newErrors.password = "Password must be at least 6 characters long";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateGroupRequest = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!groupRequest.requestedGroupName.trim()) {
      newErrors.requestedGroupName = "Group name is required";
    } else if (groupRequest.requestedGroupName.trim().length < 3) {
      newErrors.requestedGroupName = "Group name must be at least 3 characters";
    }
    
    if (groupRequest.groupDescription && groupRequest.groupDescription.length > 500) {
      newErrors.groupDescription = "Description must be less than 500 characters";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    setErrors({});
    
    switch (currentStep) {
      case 'user-info':
        if (validateUserInfo()) {
          setCurrentStep('province');
        }
        break;
      case 'province':
        if (locationSelection.province) {
          getCities(locationSelection.province._id);
          setCurrentStep('city');
        }
        break;
      case 'city':
        if (locationSelection.city) {
          getTownships(locationSelection.city._id);
          setCurrentStep('township');
        }
        break;
      case 'township':
        if (locationSelection.township) {
          setCurrentStep('group-request');
        }
        break;
    }
  };

  const handleBack = () => {
    setErrors({});
    setSearchQuery("");
    
    switch (currentStep) {
      case 'province':
        setCurrentStep(user ? 'province' : 'user-info');
        break;
      case 'city':
        setCurrentStep('province');
        break;
      case 'township':
        setCurrentStep('city');
        break;
      case 'group-request':
        setCurrentStep('township');
        break;
    }
  };

  const handleSubmit = async () => {
    if (!validateGroupRequest() || !locationSelection.province || !locationSelection.city || !locationSelection.township) {
      return;
    }

    try {
      const requestData: CreateGroupRequestInput = {
        userEmail: userInfo.email,
        userName: userInfo.name,
        userPhone: userInfo.phone,
        provinceId: locationSelection.province._id,
        provinceName: locationSelection.province.name,
        cityId: locationSelection.city._id,
        cityName: locationSelection.city.name,
        townshipId: locationSelection.township._id,
        townshipName: locationSelection.township.name,
        locationId: locationSelection.township._id, // Using township as location for now
        locationName: locationSelection.township.name,
        fullLocationPath: `${locationSelection.province.name} > ${locationSelection.city.name} > ${locationSelection.township.name}`,
        requestedGroupName: groupRequest.requestedGroupName.trim(),
        groupDescription: groupRequest.groupDescription?.trim()
      };

      // Only include userId if user is authenticated
      if (user?._id) {
        requestData.userId = user._id;
      } else {
        // Include password for anonymous users to register during approval
        requestData.userPassword = userInfo.password;
      }

      await createGroupRequest(requestData).unwrap();
      setCurrentStep('success');
      
      // Auto-close after success
      setTimeout(() => {
        onSuccess?.();
        handleClose();
      }, 3000);
      
    } catch (error) {
      console.error("Error submitting group request:", error);
      setErrors({ 
        submit: error instanceof Error ? error.message : "Failed to submit request. Please try again." 
      });
    }
  };

  if (!isOpen) return null;

  return createPortal(
    <>
      <AnimatePresence mode="wait">
      <motion.div
        variants={overlayVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className={cn(
          "fixed inset-0 z-50 bg-black/60 backdrop-blur-md",
          isMobile ? "flex items-end justify-center" : "flex items-center justify-center p-4"
        )}
        onClick={handleClose}
      >
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className={cn(
            "relative bg-white/95 backdrop-blur-xl shadow-2xl border border-white/20 overflow-hidden flex flex-col",
            isMobile
              ? "w-full max-w-sm h-[95vh] rounded-t-3xl rounded-b-none fixed bottom-0 left-0 right-0 mx-auto"
              : "w-full max-w-2xl h-[85vh] rounded-3xl"
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Gradient Background Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-emerald-50/50 rounded-3xl" />
          
          {/* Header */}
          <div className="relative px-6 py-4 border-b border-gray-200/50 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                    <Plus className="h-6 w-6 text-white" />
                  </div>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="absolute -top-1 -right-1"
                  >
                    <Sparkles className="h-4 w-4 text-yellow-500" />
                  </motion.div>
                </div>
                <div>
                  <h2
                    className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent"
                    style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                  >
                    Request New Group
                  </h2>
                  <p className="text-gray-600 text-sm">
                    Create a stokvel group for your community
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {currentStep !== 'user-info' && currentStep !== 'success' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBack}
                    disabled={isSubmitting}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    Back
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
            
            {/* Progress Indicator */}
            <div className="mt-4">
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                {['user-info', 'province', 'city', 'township', 'group-request'].map((step, index) => {
                  const stepNames = ['User Info', 'Province', 'City', 'Township', 'Group Details'];
                  const isActive = currentStep === step;
                  const isCompleted = ['user-info', 'province', 'city', 'township', 'group-request'].indexOf(currentStep) > index;
                  
                  if (user && step === 'user-info') return null; // Skip user info step for logged-in users
                  
                  return (
                    <div key={step} className="flex items-center">
                      <div className={cn(
                        "w-2 h-2 rounded-full transition-colors",
                        isActive ? "bg-blue-500" : isCompleted ? "bg-green-500" : "bg-gray-300"
                      )} />
                      <span className={cn(
                        "ml-1 transition-colors",
                        isActive ? "text-blue-600 font-medium" : isCompleted ? "text-green-600" : "text-gray-400"
                      )}>
                        {stepNames[index]}
                      </span>
                      {index < stepNames.length - 1 && !user && <ChevronRight className="h-3 w-3 mx-2 text-gray-300" />}
                      {index < stepNames.length - 1 && user && step !== 'user-info' && <ChevronRight className="h-3 w-3 mx-2 text-gray-300" />}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="relative overflow-y-auto scrollbar-hide flex-1 min-h-0 overscroll-contain">
            <div className={cn(
              "p-6",
              isMobile ? "pb-12" : "pb-8"
            )}>
              <AnimatePresence mode="wait">
                {/* User Info Step */}
                {currentStep === 'user-info' && (
                  <motion.div key="user-info" {...stepTransition} className="space-y-6 min-h-full">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <User className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">Tell us about yourself</h3>
                      <p className="text-gray-600">We need your contact information to process your group request</p>
                    </div>

                    <div className="space-y-4 pb-4">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                          Full Name *
                        </Label>
                        <div className="relative">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            id="name"
                            type="text"
                            value={userInfo.name}
                            onChange={(e) => setUserInfo(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="Enter your full name"
                            className={cn(
                              "pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                              errors.name && "border-red-300 focus:border-red-500",
                              isMobile ? "h-14 text-base" : "h-12"
                            )}
                          />
                        </div>
                        {errors.name && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.name}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                          Email Address *
                        </Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            id="email"
                            type="email"
                            value={userInfo.email}
                            onChange={(e) => setUserInfo(prev => ({ ...prev, email: e.target.value }))}
                            placeholder="Enter your email address"
                            className={cn(
                              "pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                              errors.email && "border-red-300 focus:border-red-500",
                              isMobile ? "h-14 text-base" : "h-12"
                            )}
                          />
                        </div>
                        {errors.email && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.email}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                          Phone Number (Optional)
                        </Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            id="phone"
                            type="tel"
                            value={userInfo.phone}
                            onChange={(e) => setUserInfo(prev => ({ ...prev, phone: e.target.value }))}
                            placeholder="Enter your phone number"
                            className={cn(
                              "pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                              errors.phone && "border-red-300 focus:border-red-500",
                              isMobile ? "h-14 text-base" : "h-12"
                            )}
                          />
                        </div>
                        {errors.phone && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.phone}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Password Field - Only for anonymous users */}
                    {!user && (
                      <div className="space-y-2">
                        <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                          Password <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                          <Input
                            id="password"
                            type="password"
                            value={userInfo.password}
                            onChange={(e) => setUserInfo(prev => ({ ...prev, password: e.target.value }))}
                            placeholder="Create a password (min. 6 characters)"
                            className={cn(
                              "border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                              errors.password && "border-red-300 focus:border-red-500",
                              isMobile ? "h-14 text-base" : "h-12"
                            )}
                          />
                        </div>
                        {errors.password && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.password}
                          </p>
                        )}
                        <p className="text-xs text-gray-500">
                          This will create your account when your group request is approved.
                        </p>
                      </div>
                    )}

                    <div className="pt-4">
                      <Button
                        onClick={handleNext}
                        className={cn(
                          "w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium touch-manipulation",
                          isMobile ? "h-14 text-base" : "h-12"
                        )}
                      >
                        Continue to Location Selection
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Button>
                    </div>
                  </motion.div>
                )}

                {/* Province Selection Step */}
                {currentStep === 'province' && (
                  <motion.div key="province" {...stepTransition} className="space-y-6">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <MapPin className="h-8 w-8 text-green-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">Select Province</h3>
                      <p className="text-gray-600">Choose the province where you want to create your group</p>
                    </div>

                    {/* Search */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="text"
                        placeholder="Search provinces..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className={cn(
                          "pl-10 border-gray-200 focus:border-green-500 focus:ring-green-500",
                          isMobile ? "h-14 text-base" : "h-12"
                        )}
                      />
                    </div>

                    {/* Province List */}
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {provincesLoading ? (
                        Array.from({ length: 4 }).map((_, i) => (
                          <Skeleton key={i} className="h-16 w-full rounded-xl" />
                        ))
                      ) : (
                        provincesData?.provinces
                          ?.filter(province =>
                            province.name.toLowerCase().includes(searchQuery.toLowerCase())
                          )
                          .map((province, index) => (
                            <motion.div
                              key={province._id}
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.05 }}
                              whileHover={{ scale: 1.02, x: 4 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => {
                                setLocationSelection(prev => ({ ...prev, province }));
                                handleNext();
                              }}
                              className="w-full p-4 text-left bg-white/50 hover:bg-green-50/50 border border-gray-200/50 hover:border-green-300/50 rounded-xl transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 cursor-pointer"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                    <span className="text-white font-semibold text-sm">{province.code}</span>
                                  </div>
                                  <div>
                                    <h4 className="font-semibold text-gray-900 group-hover:text-green-700 transition-colors">
                                      {province.name}
                                    </h4>
                                    <p className="text-sm text-gray-500">Province</p>
                                  </div>
                                </div>
                                <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-green-500 transition-colors" />
                              </div>
                            </motion.div>
                          ))
                      )}
                    </div>
                  </motion.div>
                )}

                {/* City Selection Step */}
                {currentStep === 'city' && (
                  <motion.div key="city" {...stepTransition} className="space-y-6">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Building className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">Select City</h3>
                      <p className="text-gray-600">Choose the city in {locationSelection.province?.name}</p>
                    </div>

                    {/* Search */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="text"
                        placeholder="Search cities..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className={cn(
                          "pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500",
                          isMobile ? "h-14 text-base" : "h-12"
                        )}
                      />
                    </div>

                    {/* City List */}
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {citiesLoading ? (
                        Array.from({ length: 4 }).map((_, i) => (
                          <Skeleton key={i} className="h-16 w-full rounded-xl" />
                        ))
                      ) : (
                        citiesData?.cities
                          ?.filter(city =>
                            city.name.toLowerCase().includes(searchQuery.toLowerCase())
                          )
                          .map((city, index) => (
                            <motion.div
                              key={city._id}
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.05 }}
                              whileHover={{ scale: 1.02, x: 4 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => {
                                setLocationSelection(prev => ({ ...prev, city }));
                                handleNext();
                              }}
                              className="w-full p-4 text-left bg-white/50 hover:bg-blue-50/50 border border-gray-200/50 hover:border-blue-300/50 rounded-xl transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                                    <Building className="h-5 w-5 text-white" />
                                  </div>
                                  <div>
                                    <h4 className="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">
                                      {city.name}
                                    </h4>
                                    <p className="text-sm text-gray-500">City in {locationSelection.province?.name}</p>
                                  </div>
                                </div>
                                <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
                              </div>
                            </motion.div>
                          ))
                      )}
                    </div>
                  </motion.div>
                )}

                {/* Township Selection Step */}
                {currentStep === 'township' && (
                  <motion.div key="township" {...stepTransition} className="space-y-6">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Home className="h-8 w-8 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">Select Township</h3>
                      <p className="text-gray-600">Choose the township in {locationSelection.city?.name}</p>
                    </div>

                    {/* Search */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="text"
                        placeholder="Search townships..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className={cn(
                          "pl-10 border-gray-200 focus:border-purple-500 focus:ring-purple-500",
                          isMobile ? "h-14 text-base" : "h-12"
                        )}
                      />
                    </div>

                    {/* Township List */}
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {townshipsLoading ? (
                        Array.from({ length: 4 }).map((_, i) => (
                          <Skeleton key={i} className="h-16 w-full rounded-xl" />
                        ))
                      ) : (
                        townshipsData?.townships
                          ?.filter(township =>
                            township.name.toLowerCase().includes(searchQuery.toLowerCase())
                          )
                          .map((township, index) => (
                            <motion.div
                              key={township._id}
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.05 }}
                              whileHover={{ scale: 1.02, x: 4 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => {
                                setLocationSelection(prev => ({ ...prev, township }));
                                handleNext();
                              }}
                              className="w-full p-4 text-left bg-white/50 hover:bg-purple-50/50 border border-gray-200/50 hover:border-purple-300/50 rounded-xl transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 cursor-pointer"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                    <Home className="h-5 w-5 text-white" />
                                  </div>
                                  <div>
                                    <h4 className="font-semibold text-gray-900 group-hover:text-purple-700 transition-colors">
                                      {township.name}
                                    </h4>
                                    <p className="text-sm text-gray-500">Township in {locationSelection.city?.name}</p>
                                  </div>
                                </div>
                                <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-purple-500 transition-colors" />
                              </div>
                            </motion.div>
                          ))
                      )}
                    </div>
                  </motion.div>
                )}

                {/* Group Request Form Step */}
                {currentStep === 'group-request' && (
                  <motion.div key="group-request" {...stepTransition} className="space-y-6">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Users className="h-8 w-8 text-emerald-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">Group Details</h3>
                      <p className="text-gray-600">Tell us about the group you want to create</p>
                    </div>

                    {/* Location Summary */}
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl">
                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">Selected Location</span>
                      </div>
                      <p className="text-sm text-blue-700">
                        {locationSelection.province?.name} → {locationSelection.city?.name} → {locationSelection.township?.name}
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="groupName" className="text-sm font-medium text-gray-700">
                          Group Name *
                        </Label>
                        <Input
                          id="groupName"
                          type="text"
                          value={groupRequest.requestedGroupName}
                          onChange={(e) => setGroupRequest(prev => ({ ...prev, requestedGroupName: e.target.value }))}
                          placeholder="Enter your group name"
                          className={cn(
                            "border-gray-200 focus:border-emerald-500 focus:ring-emerald-500",
                            errors.requestedGroupName && "border-red-300 focus:border-red-500",
                            isMobile ? "h-14 text-base" : "h-12"
                          )}
                          maxLength={50}
                        />
                        {errors.requestedGroupName && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.requestedGroupName}
                          </p>
                        )}
                        <p className="text-xs text-gray-500">
                          {groupRequest.requestedGroupName.length}/50 characters
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="groupDescription" className="text-sm font-medium text-gray-700">
                          Description (Optional)
                        </Label>
                        <Textarea
                          id="groupDescription"
                          value={groupRequest.groupDescription}
                          onChange={(e) => setGroupRequest(prev => ({ ...prev, groupDescription: e.target.value }))}
                          placeholder="Briefly describe your group's purpose, goals, or any special focus areas..."
                          className={cn(
                            "border-gray-200 focus:border-emerald-500 focus:ring-emerald-500 resize-none",
                            errors.groupDescription && "border-red-300 focus:border-red-500"
                          )}
                          maxLength={500}
                          rows={4}
                        />
                        {errors.groupDescription && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.groupDescription}
                          </p>
                        )}
                        <p className="text-xs text-gray-500">
                          {groupRequest.groupDescription?.length || 0}/500 characters
                        </p>
                      </div>
                    </div>

                    {/* Info Alert */}
                    <div className="p-4 bg-amber-50 border border-amber-200 rounded-xl">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                        <div className="text-sm text-amber-800">
                          <p className="font-medium mb-1">Request Review Process</p>
                          <p>Your request will be reviewed by our admin team. Once approved, you'll become the group admin and can start inviting members to join your stokvel.</p>
                        </div>
                      </div>
                    </div>

                    {/* Submit Error */}
                    {errors.submit && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-xl">
                        <div className="flex items-start gap-3">
                          <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                          <p className="text-sm text-red-800">{errors.submit}</p>
                        </div>
                      </div>
                    )}

                    <div className="pt-4">
                      <Button
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className={cn(
                          "w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-medium touch-manipulation",
                          isMobile ? "h-14 text-base" : "h-12"
                        )}
                      >
                      {isSubmitting ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Submitting Request...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Submit Group Request
                        </div>
                      )}
                      </Button>
                    </div>
                  </motion.div>
                )}

                {/* Success Step */}
                {currentStep === 'success' && (
                  <motion.div key="success" {...stepTransition} className="text-center py-8">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", damping: 15, stiffness: 300, delay: 0.2 }}
                      className="w-20 h-20 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6"
                    >
                      <CheckCircle className="h-10 w-10 text-green-600" />
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                    >
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">Request Submitted Successfully!</h3>
                      <p className="text-gray-600 mb-6 max-w-md mx-auto">
                        Your group request for <span className="font-semibold text-emerald-600">{groupRequest.requestedGroupName}</span> has been sent to our admin team for review.
                      </p>

                      <div className="space-y-3 text-sm text-gray-600 mb-6">
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span>You'll receive an email confirmation shortly</span>
                        </div>
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span>Admin review typically takes 1-2 business days</span>
                        </div>
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span>You'll be notified once your group is approved</span>
                        </div>
                      </div>

                      <p className="text-xs text-gray-500">
                        This window will close automatically in a few seconds...
                      </p>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </motion.div>
      </motion.div>
      </AnimatePresence>
    </>,
    document.body
  );
}
